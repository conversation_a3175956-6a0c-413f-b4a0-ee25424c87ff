using WTO.IdbSubmissions.Application.Common.Models;

namespace WTO.IdbSubmissions.Application.Features.Users.Queries;

/// <summary>
/// Query to get a user by ID
/// </summary>
public class GetUserQuery
{
    /// <summary>
    /// User ID to retrieve
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Response for GetUser query
/// </summary>
public class GetUserResponse
{
    /// <summary>
    /// User's ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User's email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's age
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// Indicates if the user is over 18
    /// </summary>
    public bool IsOver18 { get; set; }

    /// <summary>
    /// Indicates if the user is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Date when the user was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date when the user was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Handler for GetUser query
/// </summary>
public class GetUserHandler
{
    /// <summary>
    /// Handles the GetUser query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the user response</returns>
    public async Task<Result<GetUserResponse>> HandleAsync(GetUserQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            if (query.Id == Guid.Empty)
            {
                return Result<GetUserResponse>.Failure("User ID is required");
            }

            // In a real application, you would query the database here
            // For now, we'll simulate finding a user
            await Task.Delay(10, cancellationToken); // Simulate async operation

            // Simulate user not found for certain IDs
            if (query.Id.ToString().StartsWith("00000000"))
            {
                return Result<GetUserResponse>.Failure(new List<string> { $"No user found with ID: {query.Id}" });
            }

            // Create a mock user response
            var response = new GetUserResponse
            {
                Id = query.Id,
                FirstName = "John",
                LastName = "Doe",
                FullName = "John Doe",
                Email = "<EMAIL>",
                Age = 30,
                IsOver18 = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-5)
            };

            return Result<GetUserResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetUserResponse>.Failure($"Failed to retrieve user: {ex.Message}");
        }
    }
}
