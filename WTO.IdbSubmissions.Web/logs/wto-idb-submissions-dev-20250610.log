2025-06-10 15:47:46.583 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:46.798 +02:00 [INF] Registered 7 endpoints in 102 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:46.854 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:47.062 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:47.064 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:47.065 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:47.066 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:47:55.718 +02:00 [INF] Application is shutting down... <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:33704> <t:3>
2025-06-10 15:47:55.728 +02:00 [INF] Shutting down WTO IDB Submissions application <s:> <m:22MRLS3> <p:33704> <t:1>
2025-06-10 15:50:40.580 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:40.789 +02:00 [INF] Registered 7 endpoints in 97 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:40.852 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:41.087 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:41.090 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:41.091 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:41.092 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:50:52.686 +02:00 [WRN] Failed to determine the https port for redirect. <s:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware> <m:22MRLS3> <p:35656> <t:7>
2025-06-10 15:50:52.751 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:35656> <t:7>
2025-06-10 15:50:52.969 +02:00 [ERR] Message contains error: 'invalid_request', error_description: 'Invalid parameter: redirect_uri', error_uri: 'error_uri is null', status code '400'. <s:Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler> <m:22MRLS3> <p:35656> <t:9>
2025-06-10 15:50:52.973 +02:00 [ERR] Handled GET / responded 500 in 283.3990 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:35656> <t:9>
Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectProtocolException: Message contains error: 'invalid_request', error_description: 'Invalid parameter: redirect_uri', error_uri: 'error_uri is null'.
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.GetPushedAuthorizationRequestUri(HttpResponseMessage parResponseMessage)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.PushAuthorizationRequest(OpenIdConnectMessage authorizeRequest, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.HandleChallengeAsyncInternal(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.HandleChallengeAsync(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.ChallengeAsync(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
2025-06-10 15:50:52.989 +02:00 [ERR] An unhandled exception has occurred while executing the request. <s:Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware> <m:22MRLS3> <p:35656> <t:9>
Microsoft.IdentityModel.Protocols.OpenIdConnect.OpenIdConnectProtocolException: Message contains error: 'invalid_request', error_description: 'Invalid parameter: redirect_uri', error_uri: 'error_uri is null'.
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.GetPushedAuthorizationRequestUri(HttpResponseMessage parResponseMessage)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.PushAuthorizationRequest(OpenIdConnectMessage authorizeRequest, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.HandleChallengeAsyncInternal(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectHandler.HandleChallengeAsync(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.ChallengeAsync(AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-10 15:51:25.157 +02:00 [INF] Handled GET /health responded 404 in 6.8378 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:35656> <t:15>
2025-06-10 15:51:49.517 +02:00 [INF] Handled GET /api/health responded 200 in 32.3271 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:35656> <t:17>
2025-06-10 15:52:04.580 +02:00 [INF] Application is shutting down... <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:35656> <t:3>
2025-06-10 15:52:04.588 +02:00 [INF] Shutting down WTO IDB Submissions application <s:> <m:22MRLS3> <p:35656> <t:1>
2025-06-10 15:52:54.042 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:54.868 +02:00 [INF] Registered 7 endpoints in 343 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:54.989 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:55.602 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:55.605 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:55.609 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:55.611 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:55.613 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26232> <t:1>
2025-06-10 15:52:56.076 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:52:56.076 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.334 +02:00 [INF] Handled GET / responded 302 in 432.5032 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:52:56.334 +02:00 [INF] Handled GET / responded 302 in 432.5373 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.650 +02:00 [INF] Handled POST /signin-oidc responded 302 in 172.3284 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.666 +02:00 [DBG] User has 5 groups: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:52:56.670 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:52:56.674 +02:00 [INF] Handled POST /signin-oidc responded 302 in 21.0119 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.678 +02:00 [INF] Handled GET / responded 302 in 17.3119 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:52:56.683 +02:00 [DBG] User has 5 groups: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.685 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.687 +02:00 [INF] Handled GET / responded 302 in 4.8347 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:52:56.691 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 6.3813 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:9>
2025-06-10 15:52:56.696 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 0.6875 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:9>
2025-06-10 15:53:02.290 +02:00 [DBG] User has 5 groups: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:53:02.294 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:53:02.300 +02:00 [INF] Handled GET / responded 302 in 10.1794 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:7>
2025-06-10 15:53:02.312 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 1.1099 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26232> <t:11>
2025-06-10 15:57:00.539 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:01.332 +02:00 [INF] Registered 7 endpoints in 319 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:01.483 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.210 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.212 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.215 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.217 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.219 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:1368> <t:1>
2025-06-10 15:57:02.614 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:1368> <t:10>
2025-06-10 15:57:02.869 +02:00 [INF] Handled GET / responded 302 in 439.9826 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:1368> <t:7>
2025-06-10 16:52:27.411 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.080 +02:00 [INF] Registered 7 endpoints in 280 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.193 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.657 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.658 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.660 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.662 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:28.663 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:26932> <t:1>
2025-06-10 16:52:29.325 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:52:29.543 +02:00 [INF] Handled GET / responded 302 in 386.9892 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:53:20.449 +02:00 [INF] Handled POST /signin-oidc responded 302 in 50791.6052 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:53:20.458 +02:00 [DBG] User has 6 groups: /Admin, create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:53:20.459 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: /Admin, create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:53:20.461 +02:00 [INF] Handled GET / responded 302 in 6.6302 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26932> <t:7>
2025-06-10 16:53:20.471 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 4.7531 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:26932> <t:11>
2025-06-10 16:55:08.896 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:09.589 +02:00 [INF] Registered 7 endpoints in 296 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:09.709 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.206 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.208 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.210 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.211 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.212 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:40476> <t:1>
2025-06-10 16:55:10.614 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:40476> <t:7>
2025-06-10 16:55:10.850 +02:00 [INF] Handled GET / responded 302 in 394.4434 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:55:40.590 +02:00 [INF] Handled POST /signin-oidc responded 302 in 29630.0541 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:55:40.598 +02:00 [DBG] User has 10 groups: create-realm, default-roles-master, offline_access, admin, uma_authorization, create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:55:40.599 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: create-realm, default-roles-master, offline_access, admin, uma_authorization, create-realm, default-roles-master, offline_access, admin, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:55:40.602 +02:00 [INF] Handled GET / responded 302 in 7.0524 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:55:40.612 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 5.2313 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:40476> <t:9>
2025-06-10 16:58:37.693 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:38.482 +02:00 [INF] Registered 7 endpoints in 326 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:38.596 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.165 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.167 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.171 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.174 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.175 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:34788> <t:1>
2025-06-10 16:58:39.795 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:34788> <t:9>
2025-06-10 16:58:40.092 +02:00 [INF] Handled GET / responded 302 in 557.9070 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:34788> <t:9>
2025-06-10 16:59:11.342 +02:00 [INF] Handled POST /signin-oidc responded 302 in 22340.0722 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:34788> <t:7>
2025-06-10 16:59:11.350 +02:00 [DBG] User has 4 groups: /IDB_ADMIN_USER, default-roles-master, offline_access, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:34788> <t:11>
2025-06-10 16:59:11.351 +02:00 [WRN] User does not have required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER, User has: /IDB_ADMIN_USER, default-roles-master, offline_access, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:34788> <t:11>
2025-06-10 16:59:11.353 +02:00 [INF] Handled GET / responded 302 in 6.0535 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:34788> <t:11>
2025-06-10 16:59:11.361 +02:00 [INF] Handled GET /Account/AccessDenied responded 404 in 4.2885 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:34788> <t:7>
2025-06-10 17:00:35.524 +02:00 [INF] Starting WTO IDB Submissions application <s:> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:36.241 +02:00 [INF] Registered 7 endpoints in 331 milliseconds. <s:FastEndpoints.StartupTimer> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:36.371 +02:00 [INF] No validators found in the system! <s:FastEndpoints.Swagger.ValidationSchemaProcessor> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.247 +02:00 [INF] Now listening on: https://localhost:7056 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.255 +02:00 [INF] Now listening on: http://localhost:5012 <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.259 +02:00 [INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.261 +02:00 [INF] Hosting environment: Development <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.263 +02:00 [INF] Content root path: C:\Projects\WTO.IdbSubmissions\WTO.IdbSubmissions.Web <s:Microsoft.Hosting.Lifetime> <m:22MRLS3> <p:6224> <t:1>
2025-06-10 17:00:37.625 +02:00 [DBG] User is not authenticated <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:6224> <t:11>
2025-06-10 17:00:37.927 +02:00 [INF] Handled GET / responded 302 in 487.7515 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:11>
2025-06-10 17:01:08.258 +02:00 [INF] Handled POST /signin-oidc responded 302 in 15840.7403 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.265 +02:00 [DBG] User has 4 groups: IDB_ADMIN_USER, default-roles-master, offline_access, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.267 +02:00 [DBG] User has required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.288 +02:00 [INF] Home page accessed by user Edward Carandang at "2025-06-10T15:01:08.2884681Z" from Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* <s:WTO.IdbSubmissions.Web.Controllers.HomeController> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.388 +02:00 [INF] Handled GET / responded 200 in 125.2684 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:9>
2025-06-10 17:01:08.586 +02:00 [INF] Handled GET /css/site.css responded 200 in 194.8651 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.586 +02:00 [INF] Handled GET /js/site.js responded 200 in 153.8418 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:19>
2025-06-10 17:01:08.587 +02:00 [INF] Handled GET /WTO.IdbSubmissions.Web.styles.css responded 200 in 179.1178 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:17>
2025-06-10 17:01:08.593 +02:00 [INF] Handled GET /lib/jquery/dist/jquery.min.js responded 200 in 185.9365 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:21>
2025-06-10 17:01:08.593 +02:00 [INF] Handled GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 202.4511 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:08.593 +02:00 [INF] Handled GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 171.3224 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:9>
2025-06-10 17:01:08.708 +02:00 [INF] Handled GET /favicon.ico responded 200 in 2.7745 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:21>
2025-06-10 17:01:12.352 +02:00 [DBG] User has 4 groups: IDB_ADMIN_USER, default-roles-master, offline_access, uma_authorization <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:12.354 +02:00 [DBG] User has required group membership. Required: IDB_MEMBER_USER, IDB_ADMIN_USER <s:WTO.IdbSubmissions.Web.Authorization.Handlers.AdGroupAuthorizationHandler> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:12.355 +02:00 [INF] Home page accessed by user Edward Carandang at "2025-06-10T15:01:12.3558314Z" from Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* <s:WTO.IdbSubmissions.Web.Controllers.HomeController> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:12.360 +02:00 [INF] Handled GET / responded 200 in 8.3085 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:9>
2025-06-10 17:01:12.386 +02:00 [INF] Handled GET /WTO.IdbSubmissions.Web.styles.css responded 200 in 1.9202 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:12.387 +02:00 [INF] Handled GET /css/site.css responded 200 in 3.3696 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:10>
2025-06-10 17:01:12.388 +02:00 [INF] Handled GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 7.0700 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:17>
2025-06-10 17:01:12.388 +02:00 [INF] Handled GET /js/site.js responded 200 in 3.3306 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:9>
2025-06-10 17:01:12.416 +02:00 [INF] Handled GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 32.3723 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:14>
2025-06-10 17:01:12.417 +02:00 [INF] Handled GET /lib/jquery/dist/jquery.min.js responded 200 in 32.8070 ms <s:Serilog.AspNetCore.RequestLoggingMiddleware> <m:22MRLS3> <p:6224> <t:18>
