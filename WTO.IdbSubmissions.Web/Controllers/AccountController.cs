using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Account controller for authentication-related actions
/// </summary>
public class AccountController : Controller
{
    private readonly ILogger<AccountController> _logger;

    public AccountController(ILogger<AccountController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Initiates the login process by challenging the OpenID Connect authentication
    /// </summary>
    /// <param name="returnUrl">URL to redirect to after successful login</param>
    /// <returns>Challenge result</returns>
    [AllowAnonymous]
    public IActionResult Login(string? returnUrl = null)
    {
        _logger.LogInformation("Login initiated with return URL: {ReturnUrl}", returnUrl ?? "None");
        
        var redirectUrl = Url.Action(nameof(LoginCallback), "Account", new { returnUrl });
        var properties = new AuthenticationProperties { RedirectUri = redirectUrl };
        
        return Challenge(properties, OpenIdConnectDefaults.AuthenticationScheme);
    }

    /// <summary>
    /// Handles the callback from ADFS after authentication
    /// </summary>
    /// <param name="returnUrl">URL to redirect to after successful login</param>
    /// <returns>Redirect result</returns>
    [AllowAnonymous]
    public IActionResult LoginCallback(string? returnUrl = null)
    {
        _logger.LogInformation("Login callback received for user {UserName} with return URL: {ReturnUrl}", 
            User.Identity?.Name ?? "Unknown", returnUrl ?? "None");

        if (!User.Identity?.IsAuthenticated ?? true)
        {
            _logger.LogWarning("Login callback received but user is not authenticated");
            return RedirectToAction("Index", "Home");
        }

        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            return Redirect(returnUrl);
        }

        return RedirectToAction("Index", "Home");
    }

    /// <summary>
    /// Logs out the user from both the local application and ADFS
    /// </summary>
    /// <returns>Sign out result</returns>
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        var userName = User.Identity?.Name ?? "Unknown";
        _logger.LogInformation("Logout initiated for user {UserName}", userName);

        // Sign out from the local cookie authentication
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        
        // Sign out from ADFS (this will redirect to ADFS logout page)
        var properties = new AuthenticationProperties
        {
            RedirectUri = Url.Action("LogoutCallback", "Account")
        };

        _logger.LogInformation("Signing out user {UserName} from ADFS", userName);
        
        return SignOut(properties, OpenIdConnectDefaults.AuthenticationScheme);
    }

    /// <summary>
    /// Handles the callback from ADFS after logout
    /// </summary>
    /// <returns>Redirect to home page</returns>
    [AllowAnonymous]
    public IActionResult LogoutCallback()
    {
        _logger.LogInformation("Logout callback received, redirecting to home page");
        return RedirectToAction("Index", "Home");
    }

    /// <summary>
    /// Access denied page
    /// </summary>
    /// <returns>Access denied view</returns>
    [AllowAnonymous]
    public IActionResult AccessDenied()
    {
        _logger.LogWarning("Access denied page accessed by user {UserName}", User.Identity?.Name ?? "Anonymous");
        return View();
    }
}
