using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTO.IdbSubmissions.Web.Authorization.Constants;

namespace WTO.IdbSubmissions.Web.Controllers;

/// <summary>
/// Base controller with authentication required
/// Inherit from this for controllers that require authentication but no specific authorization policy
/// </summary>
[Authorize(Policy = AuthorizationPolicies.IdbUserPolicy)]
public class BaseMVCController : Controller
{

}

/// <summary>
/// Base controller for IDB Member users
/// Inherit from this for controllers that require IDB Member access
/// </summary>
[Authorize(Policy = AuthorizationPolicies.IdbMemberPolicy)]
public class IdbMemberController : BaseMVCController
{

}

/// <summary>
/// Base controller for IDB Admin users
/// Inherit from this for controllers that require IDB Admin access
/// </summary>
[Authorize(Policy = AuthorizationPolicies.IdbAdminPolicy)]
public class IdbAdminController : BaseMVCController
{

}
