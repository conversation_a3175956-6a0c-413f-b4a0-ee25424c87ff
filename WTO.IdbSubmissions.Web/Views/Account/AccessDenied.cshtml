@{
    ViewData["Title"] = "Access Denied";
}

<div class="text-center">
    <h1 class="display-1 text-danger">403</h1>
    <p class="fs-3"><span class="text-danger">Oops!</span> Access Denied.</p>
    <p class="lead">
        You don't have permission to access this resource.
    </p>
    <div class="mt-4">
        <p>This could be because:</p>
        <ul class="list-unstyled">
            <li>• You don't have the required group membership</li>
            <li>• Your session has expired</li>
            <li>• You need to contact an administrator for access</li>
        </ul>
    </div>
    <div class="mt-4">
        <a asp-controller="Home" asp-action="Index" class="btn btn-primary">Go Home</a>
        @if (User.Identity?.IsAuthenticated == true)
        {
            <a asp-controller="Account" asp-action="Logout" class="btn btn-outline-secondary ms-2">Sign Out</a>
        }
        else
        {
            <a asp-controller="Account" asp-action="Login" class="btn btn-outline-primary ms-2">Sign In</a>
        }
    </div>
</div>
