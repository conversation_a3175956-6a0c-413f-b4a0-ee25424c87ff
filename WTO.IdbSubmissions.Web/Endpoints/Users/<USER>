using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.Users.Commands;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.Users;

/// <summary>
/// Endpoint for creating a new user
/// </summary>
public class CreateUserEndpoint : BaseEndpoint<CreateUserCommand, CreateUserResponse>
{
    private readonly CreateUserHandler _handler;

    /// <summary>
    /// Initializes a new instance of the CreateUserEndpoint
    /// </summary>
    /// <param name="handler">Create user handler</param>
    public CreateUserEndpoint(CreateUserHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    public override void Configure()
    {
        Post("/users");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Create a new user";
            s.Description = "Creates a new user with the provided information";
            s.ExampleRequest = new CreateUserCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Age = 30
            };
            s.Responses[200] = "User created successfully";
            s.Responses[400] = "Invalid request data";
        });
    }

    public override async Task HandleAsync(CreateUserCommand req, CancellationToken ct)
    {
        var result = await _handler.HandleAsync(req, ct);
        await HandleResultAsync(result, "User created successfully", ct);
    }
}
