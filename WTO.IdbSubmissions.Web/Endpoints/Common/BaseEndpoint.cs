using FastEndpoints;
using WTO.IdbSubmissions.Application.Common.Models;

namespace WTO.IdbSubmissions.Web.Endpoints.Common;

/// <summary>
/// Base endpoint class providing common functionality for FastEndpoints
/// </summary>
/// <typeparam name="TRequest">Request DTO type</typeparam>
/// <typeparam name="TResponse">Response DTO type</typeparam>
public abstract class BaseEndpoint<TRequest, TResponse> : Endpoint<TRequest, ApiResponse<TResponse>>
    where TRequest : notnull, new()
    where TResponse : notnull, new()
{
    /// <summary>
    /// Sends a successful response with data
    /// </summary>
    /// <param name="data">Response data</param>
    /// <param name="message">Success message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendSuccessAsync(TResponse data, string message = "Operation completed successfully", CancellationToken cancellationToken = default)
    {
        var response = ApiResponse<TResponse>.SuccessResponse(data, message);
        await SendOkAsync(response, cancellationToken);
    }

    /// <summary>
    /// Sends an error response
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="errors">List of specific errors</param>
    /// <param name="statusCode">HTTP status code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendErrorAsync(string message, List<string>? errors = null, int statusCode = 400, CancellationToken cancellationToken = default)
    {
        var response = ApiResponse<TResponse>.ErrorResponse(message, errors);
        await SendAsync(response, statusCode, cancellationToken);
    }

    /// <summary>
    /// Handles a Result<T> and sends appropriate response
    /// </summary>
    /// <param name="result">Result to handle</param>
    /// <param name="successMessage">Message for successful operations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task HandleResultAsync(Result<TResponse> result, string successMessage = "Operation completed successfully", CancellationToken cancellationToken = default)
    {
        if (result.IsSuccess)
        {
            await SendSuccessAsync(result.Data!, successMessage, cancellationToken);
        }
        else
        {
            await SendErrorAsync(result.ErrorMessage ?? "An error occurred", result.Errors.ToList(), cancellationToken: cancellationToken);
        }
    }
}

/// <summary>
/// Base endpoint class for endpoints without request data
/// </summary>
/// <typeparam name="TResponse">Response DTO type</typeparam>
public abstract class BaseEndpoint<TResponse> : EndpointWithoutRequest<ApiResponse<TResponse>>
    where TResponse : notnull, new()
{
    /// <summary>
    /// Sends a successful response with data
    /// </summary>
    /// <param name="data">Response data</param>
    /// <param name="message">Success message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendSuccessAsync(TResponse data, string message = "Operation completed successfully", CancellationToken cancellationToken = default)
    {
        var response = ApiResponse<TResponse>.SuccessResponse(data, message);
        await SendOkAsync(response, cancellationToken);
    }

    /// <summary>
    /// Sends an error response
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="errors">List of specific errors</param>
    /// <param name="statusCode">HTTP status code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendErrorAsync(string message, List<string>? errors = null, int statusCode = 400, CancellationToken cancellationToken = default)
    {
        var response = ApiResponse<TResponse>.ErrorResponse(message, errors);
        await SendAsync(response, statusCode, cancellationToken);
    }

    /// <summary>
    /// Handles a Result<T> and sends appropriate response
    /// </summary>
    /// <param name="result">Result to handle</param>
    /// <param name="successMessage">Message for successful operations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task HandleResultAsync(Result<TResponse> result, string successMessage = "Operation completed successfully", CancellationToken cancellationToken = default)
    {
        if (result.IsSuccess)
        {
            await SendSuccessAsync(result.Data!, successMessage, cancellationToken);
        }
        else
        {
            await SendErrorAsync(result.ErrorMessage ?? "An error occurred", result.Errors.ToList(), cancellationToken: cancellationToken);
        }
    }
}

/// <summary>
/// Base endpoint class for endpoints without request or response data
/// </summary>
public abstract class BaseEndpoint : EndpointWithoutRequest<ApiResponse>
{
    /// <summary>
    /// Sends a successful response without data
    /// </summary>
    /// <param name="message">Success message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendSuccessAsync(string message = "Operation completed successfully", CancellationToken cancellationToken = default)
    {
        var response = ApiResponse.SuccessResponse(message);
        await SendOkAsync(response, cancellationToken);
    }

    /// <summary>
    /// Sends an error response
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="errors">List of specific errors</param>
    /// <param name="statusCode">HTTP status code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task SendErrorAsync(string message, List<string>? errors = null, int statusCode = 400, CancellationToken cancellationToken = default)
    {
        var response = ApiResponse.ErrorResponse(message, errors);
        await SendAsync(response, statusCode, cancellationToken);
    }
}
