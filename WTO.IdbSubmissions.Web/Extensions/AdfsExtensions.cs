using System.Security.Claims;
using IdentityModel;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;

namespace WTO.IdbSubmissions.Web.Extensions;

public static class AdfsExtensions
{
	public static IServiceCollection AddWTOADFSAuthentication(
		this IServiceCollection services,
		IConfiguration          configuration
	)
	{
		services.AddAuthentication(options =>
		        {
			        options.DefaultScheme          = CookieAuthenticationDefaults.AuthenticationScheme;
			        options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
		        })
		        .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
		        {
			        options.ExpireTimeSpan    = TimeSpan.FromHours(12); // Set your desired expiration
			        options.SlidingExpiration = true;
			        options.Events.OnValidatePrincipal = async context =>
			        {
				        var claimsPrincipal = context.Principal;
				        var claimsIdentity  = claimsPrincipal?.Identity as ClaimsIdentity;

				        // Check if the claims expiration has passed
				        var expClaim = claimsIdentity?.FindFirst("exp"); // adjust if different claim type
				        if (expClaim != null)
				        {
					        var expDateUnix    = long.Parse(expClaim.Value);
					        var expDateTimeUtc = DateTimeOffset.FromUnixTimeSeconds(expDateUnix).UtcDateTime;

					        if (DateTime.UtcNow > expDateTimeUtc)
					        {
						        context.RejectPrincipal(); // Expired claims
						        await context.HttpContext.SignOutAsync();
					        }
				        }
			        };

			        options.Events.OnRedirectToLogin = context =>
			        {
				        if (context.Request.Path.StartsWithSegments("/_blazor"))
				        {
					        context.Response.StatusCode = 401;
					        return Task.CompletedTask;
				        }

				        context.Response.Redirect(context.RedirectUri);
				        return Task.CompletedTask;
			        };
		        })
		        .AddOpenIdConnect(OpenIdConnectDefaults.AuthenticationScheme, options =>
		        {
			        options.DisableTelemetry     = true;
			        options.UseTokenLifetime     = true;

			        #if DEBUG
			        
			        options.RequireHttpsMetadata = false;
			        
			        #endif

			        options.MapInboundClaims = false;
			        options.SignInScheme     = CookieAuthenticationDefaults.AuthenticationScheme;
			        options.Authority        = configuration.GetValue<string>("ADFSSettings:Authority");
			        options.ClientId         = configuration.GetValue<string>("ADFSSettings:ClientId");
			        options.ResponseType     = "code";
			        options.UsePkce          = true;
			        // options.SaveTokens = true;

			        //options.CallbackPath = new PathString("...");
			        options.Scope.Add("openid");
			        options.Scope.Add("profile");
			        options.Scope.Add("email");

			        options.ClaimActions.DeleteClaim("sid");
			        options.ClaimActions.DeleteClaim("idp");
			        options.ClaimActions.DeleteClaim("s_hash");
			        options.ClaimActions.DeleteClaim("auth_time");

			        options.ClientSecret                  = configuration.GetValue<string>("ADFSSettings:Secret");
			        options.GetClaimsFromUserInfoEndpoint = false;

			        // Increase the timeouts
			        options.BackchannelTimeout                        = TimeSpan.FromSeconds(60);
			        options.ProtocolValidator.RequireTimeStampInNonce = true;
			        options.ProtocolValidator.NonceLifetime           = TimeSpan.FromMinutes(15);

			        // Configure response mode explicitly
			        options.ResponseMode = OpenIdConnectResponseMode.FormPost;

			        // Add state parameter handling
			        options.UseSecurityTokenValidator = true;

			        options.TokenValidationParameters = new TokenValidationParameters
			        {
				        NameClaimType    = JwtClaimTypes.Name,
				        RoleClaimType    = JwtClaimTypes.Role,
				        ValidateIssuer   = true,
				        ValidateLifetime = true,
				        ValidateAudience = true,
			        };

			        options.NonceCookie.SameSite       = SameSiteMode.None;
			        options.CorrelationCookie.SameSite = SameSiteMode.None;

			        options.Events = new OpenIdConnectEvents
			        {
				        OnRemoteFailure = context =>
				        {
					        context.Response.Redirect("/");

					        return Task.FromResult(0);
				        },
				        OnTokenValidated = context =>
				        {
					        var identity = (ClaimsIdentity)context.Principal?.Identity!;

					        return Task.FromResult(0);
				        },
				        OnTokenResponseReceived = context =>
				        {
					        Console.WriteLine($"Received Token: {context.TokenEndpointResponse.AccessToken}...");
					        return Task.FromResult(0);
				        },
				        OnRedirectToIdentityProvider = context =>
				        {
					        // Ensure only one redirect occurs
					        if (context.Properties.Items.ContainsKey("RedirectOccurred"))
					        {
						        context.HandleResponse();
						        return Task.CompletedTask;
					        }

					        context.Properties.Items["RedirectOccurred"] = "true";
					        return Task.CompletedTask;
				        }
			        };
		        });

		return services;
	}
}