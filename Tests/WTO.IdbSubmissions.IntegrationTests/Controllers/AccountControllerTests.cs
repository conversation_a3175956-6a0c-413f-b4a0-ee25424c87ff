using System.Net;
using WTO.IdbSubmissions.IntegrationTests.WebApplicationFactory;

namespace WTO.IdbSubmissions.IntegrationTests.Controllers;

/// <summary>
/// Integration tests for AccountController
/// </summary>
public class AccountControllerTests : IntegrationTestBase
{
    public AccountControllerTests(CustomWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task Login_ShouldRedirectToADFS()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Account/Login");

        // Assert
        // Should redirect to ADFS (302 Found)
        Assert.Equal(HttpStatusCode.Found, response.StatusCode);
        Assert.NotNull(response.Headers.Location);
    }

    [Fact]
    public async Task Logout_WithoutAuthentication_ShouldRedirectToLogin()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Account/Logout");

        // Assert
        // Should redirect to login since user is not authenticated
        Assert.Equal(HttpStatusCode.Found, response.StatusCode);
        Assert.NotNull(response.Headers.Location);
    }

    [Fact]
    public async Task AccessDenied_ShouldReturnAccessDeniedPage()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Account/AccessDenied");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Access Denied", content);
        Assert.Contains("403", content);
    }

    [Fact]
    public async Task LoginCallback_WithoutAuthentication_ShouldRedirectToHome()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Account/LoginCallback");

        // Assert
        Assert.Equal(HttpStatusCode.Found, response.StatusCode);
        Assert.NotNull(response.Headers.Location);
        Assert.Contains("/", response.Headers.Location.ToString());
    }

    [Fact]
    public async Task LogoutCallback_ShouldRedirectToHome()
    {
        // Arrange & Act
        var response = await Client.GetAsync("/Account/LogoutCallback");

        // Assert
        Assert.Equal(HttpStatusCode.Found, response.StatusCode);
        Assert.NotNull(response.Headers.Location);
        Assert.Contains("/", response.Headers.Location.ToString());
    }
}
